<step title="Incoming Request" date="2025-07-23T19:06:48.959" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,7" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="284f732f-67f8-11f0-afc2-848f4c0f5f6b" X-ARR-LOG-ID="284f732f-67f8-11f0-afc2-848f4c0f5f6b" CLIENT-IP="**************:29855" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="29855" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T19:06:49.033" /><!-- duration: 39ms -->
