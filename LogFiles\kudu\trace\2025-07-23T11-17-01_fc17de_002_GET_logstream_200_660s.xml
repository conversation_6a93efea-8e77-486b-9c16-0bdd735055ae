<step title="Incoming Request" date="2025-07-23T11:17:01.362" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,15" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="86a75e34-67b6-11f0-8511-848f4c0f5f6b" X-ARR-LOG-ID="86a75e34-67b6-11f0-8511-848f4c0f5f6b" CLIENT-IP="**************:10172" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="10172" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T11:17:01.375" /><!-- duration: 67ms -->
  <step title="Error occurred" date="2025-07-23T11:28:01.463" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 11ms -->
  <step title="Outgoing response" date="2025-07-23T11:28:01.489" type="response" statusCode="200" statusText="OK" /><!-- duration: 11ms -->
</step><!-- duration: 660146ms -->
