<step title="Incoming Request" date="2025-07-23T14:09:09.080" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,34" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="930ae0d3-67ce-11f0-b143-848f4c0f5f6b" X-ARR-LOG-ID="930ae0d3-67ce-11f0-b143-848f4c0f5f6b" CLIENT-IP="**************:10991" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="10991" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:09:09.086" /><!-- duration: 5ms -->
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T14:09:09.112" /><!-- duration: 61ms -->
  <step title="Error occurred" date="2025-07-23T14:19:09.181" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 7ms -->
  <step title="Outgoing response" date="2025-07-23T14:19:09.200" type="response" statusCode="200" statusText="OK" /><!-- duration: 3ms -->
</step><!-- duration: 600128ms -->
