<step title="Incoming Request" date="2025-07-23T15:00:12.212" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,28" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="b55a5e7e-67d5-11f0-99cb-848f4c0f5f6b" X-ARR-LOG-ID="b55a5e7e-67d5-11f0-99cb-848f4c0f5f6b" CLIENT-IP="**************:29953" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="29953" >
  <step title="Cleanup Xml Logs" date="2025-07-23T15:00:12.217" /><!-- duration: 7ms -->
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T15:00:12.231" /><!-- duration: 30ms -->
  <step title="Error occurred" date="2025-07-23T15:21:12.282" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 7ms -->
  <step title="Outgoing response" date="2025-07-23T15:21:12.308" type="response" statusCode="200" statusText="OK" /><!-- duration: 2ms -->
</step><!-- duration: 1260100ms -->
