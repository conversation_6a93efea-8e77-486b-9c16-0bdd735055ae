<step title="Incoming Request" date="2025-07-23T18:41:20.027" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,14" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="99d066b7-67f4-11f0-9276-848f4c0f5f6b" X-ARR-LOG-ID="99d066b7-67f4-11f0-9276-848f4c0f5f6b" CLIENT-IP="**************:32652" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="32652" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T18:41:20.042" /><!-- duration: 35ms -->
