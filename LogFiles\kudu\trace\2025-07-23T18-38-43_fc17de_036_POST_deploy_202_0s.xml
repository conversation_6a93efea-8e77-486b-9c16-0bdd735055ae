<step title="Incoming Request" date="2025-07-23T18:38:43.736" instance="fc17de" url="/deploy?scmType=ExternalGit&amp;isAsync=true" method="POST" type="request" pid="821,1,53" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Expect="100-continue" Content-Length="87" x-ms-request-id="7e4294e4-34e6-4ffb-a4ff-1e096d7d723d" X-ARR-LOG-ID="7e4294e4-34e6-4ffb-a4ff-1e096d7d723d" CLIENT-IP="*************:48000" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/deploy?scmType=ExternalGit&amp;isAsync=true" X-Client-IP="*************" X-Client-Port="48000" >
  <step title="Cleanup Xml Logs" date="2025-07-23T18:38:43.738" /><!-- duration: 6ms -->
  <step title="FetchHandler" date="2025-07-23T18:38:43.770" >
    <step title="Executing external process" date="2025-07-23T18:38:43.878" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 56ms -->
    <step title="Executing external process" date="2025-07-23T18:38:43.941" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 14ms -->
    <step title="Executing external process" date="2025-07-23T18:38:43.956" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 65ms -->
    <step title="Start deployment in the background" date="2025-07-23T18:38:44.029" >
      <step title="Executing external process" date="2025-07-23T18:38:44.041" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 19ms -->
      <step title="Executing external process" date="2025-07-23T18:38:44.064" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 11ms -->
      <step title="Executing external process" date="2025-07-23T18:38:44.077" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 36ms -->
      <step title="Acquiring Deployment Lock" date="2025-07-23T18:38:44.115" /><!-- duration: 2ms -->
      <step title="Acquired Deployment Lock" date="2025-07-23T18:38:44.141" /><!-- duration: 2ms -->
    </step><!-- duration: 175ms -->
  </step><!-- duration: 436ms -->
  <step title="Outgoing response" date="2025-07-23T18:38:44.208" type="response" statusCode="202" statusText="Accepted" /><!-- duration: 2ms -->
</step><!-- duration: 477ms -->
