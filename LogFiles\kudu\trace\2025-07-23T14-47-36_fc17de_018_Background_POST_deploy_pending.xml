<step title="BackgroundTrace" date="2025-07-23T14:47:36.950" instance="fc17de" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-23T14:47:37.091" /><!-- duration: 2ms -->
  <step title="Creating temporary deployment" date="2025-07-23T14:47:37.096" /><!-- duration: 105ms -->
  <step title="Performing fetch based deployment" date="2025-07-23T14:47:37.332" >
    <step title="Deployment timer started" date="2025-07-23T14:47:40.377" >
      <step title="DeploymentManager.Deploy(id:f1c29e39e8014509a2c4a7551a8d334246538444)" date="2025-07-23T14:47:40.388" >
        <step title="Collecting changeset information" date="2025-07-23T14:47:40.419" /><!-- duration: 96ms -->
        <step title="Updating submodules" date="2025-07-23T14:47:40.521" /><!-- duration: 1106ms -->
        <step title="Determining deployment builder" date="2025-07-23T14:47:41.825" >
          <step title="Builder is OryxBuilder" date="2025-07-23T14:47:41.843" /><!-- duration: 9ms -->
        </step><!-- duration: 36ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-23T14:47:41.906" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-23T14:47:42.022" >
            <step title="Building" date="2025-07-23T14:47:42.145" >
              <step title="Executing external process" date="2025-07-23T14:47:42.375" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc9f7e098b0f3 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 13103ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-23T14:47:55.788" /><!-- duration: 3ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-23T14:47:56.742" /><!-- duration: 10ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-23T14:47:56.779" /><!-- duration: 5ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-23T14:47:56.790" /><!-- duration: 5ms -->
            </step><!-- duration: 15320ms -->
          </step><!-- duration: 15486ms -->
          <step title="Cleaning up temp files" date="2025-07-23T14:47:57.511" /><!-- duration: 415ms -->
          <step title="Cleaning up temp files" date="2025-07-23T14:47:57.933" /><!-- duration: 510ms -->
          <step title="Reloading status file with latest updates" date="2025-07-23T14:47:58.476" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-23T14:47:58.480" /><!-- duration: 34ms -->
          </step><!-- duration: 42ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-23T14:47:58.531" /><!-- duration: 3ms -->
        </step><!-- duration: 16684ms -->
      </step><!-- duration: 18221ms -->
