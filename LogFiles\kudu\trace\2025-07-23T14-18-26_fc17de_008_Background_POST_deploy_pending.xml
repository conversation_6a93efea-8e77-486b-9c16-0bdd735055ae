<step title="BackgroundTrace" date="2025-07-23T14:18:26.765" instance="fc17de" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-23T14:18:26.938" /><!-- duration: 2ms -->
  <step title="Creating temporary deployment" date="2025-07-23T14:18:26.943" /><!-- duration: 68ms -->
  <step title="Performing fetch based deployment" date="2025-07-23T14:18:27.166" >
    <step title="Deployment timer started" date="2025-07-23T14:18:30.151" >
      <step title="DeploymentManager.Deploy(id:1c79e7fa2f11174132690eabc52e2f6dfba502f3)" date="2025-07-23T14:18:30.158" >
        <step title="Collecting changeset information" date="2025-07-23T14:18:30.203" /><!-- duration: 93ms -->
        <step title="Updating submodules" date="2025-07-23T14:18:30.303" /><!-- duration: 1098ms -->
        <step title="Determining deployment builder" date="2025-07-23T14:18:31.594" >
          <step title="Builder is OryxBuilder" date="2025-07-23T14:18:31.604" /><!-- duration: 3ms -->
        </step><!-- duration: 16ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-23T14:18:31.659" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-23T14:18:31.751" >
            <step title="Building" date="2025-07-23T14:18:31.863" >
              <step title="Executing external process" date="2025-07-23T14:18:32.079" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc9f3cd5d7086 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 14790ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-23T14:18:47.195" /><!-- duration: 3ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-23T14:18:48.102" /><!-- duration: 8ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-23T14:18:48.135" /><!-- duration: 3ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-23T14:18:48.149" /><!-- duration: 3ms -->
            </step><!-- duration: 16945ms -->
          </step><!-- duration: 17065ms -->
          <step title="Cleaning up temp files" date="2025-07-23T14:18:48.818" /><!-- duration: 315ms -->
          <step title="Cleaning up temp files" date="2025-07-23T14:18:49.144" /><!-- duration: 511ms -->
          <step title="Reloading status file with latest updates" date="2025-07-23T14:18:49.692" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-23T14:18:49.698" /><!-- duration: 47ms -->
          </step><!-- duration: 57ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-23T14:18:49.756" /><!-- duration: 4ms -->
        </step><!-- duration: 18167ms -->
      </step><!-- duration: 19684ms -->
