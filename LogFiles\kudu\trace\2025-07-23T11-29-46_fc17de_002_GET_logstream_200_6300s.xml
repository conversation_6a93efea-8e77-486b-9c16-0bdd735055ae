<step title="Incoming Request" date="2025-07-23T11:29:46.888" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,5" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="4eaa8333-67b8-11f0-a698-848f4c0f5f6b" X-ARR-LOG-ID="4eaa8333-67b8-11f0-a698-848f4c0f5f6b" CLIENT-IP="**************:28766" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="28766" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T11:29:46.896" /><!-- duration: 35ms -->
  <step title="Error occurred" date="2025-07-23T13:14:47.070" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 21ms -->
  <step title="Outgoing response" date="2025-07-23T13:14:47.124" type="response" statusCode="200" statusText="OK" /><!-- duration: 3ms -->
</step><!-- duration: 6300242ms -->
