<step title="Incoming Request" date="2025-07-23T11:08:16.798" instance="fc17de" url="/deploy?scmType=ExternalGit&amp;isAsync=true" method="POST" type="request" pid="821,1,15" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Expect="100-continue" Content-Length="87" x-ms-request-id="cfeea8eb-3ef8-4a10-8b61-ffc6a6c37bfe" X-ARR-LOG-ID="cfeea8eb-3ef8-4a10-8b61-ffc6a6c37bfe" CLIENT-IP="*************:48002" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/deploy?scmType=ExternalGit&amp;isAsync=true" X-Client-IP="*************" X-Client-Port="48002" >
  <step title="FetchHandler" date="2025-07-23T11:08:16.842" >
    <step title="GitExeRepository.Initialize" date="2025-07-23T11:08:16.872" >
      <step title="Executing external process" date="2025-07-23T11:08:16.875" type="process" path="git" arguments="init" /><!-- duration: 416ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.294" type="process" path="git" arguments="config core.autocrlf false" /><!-- duration: 53ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.350" type="process" path="git" arguments="config core.preloadindex true" /><!-- duration: 24ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.377" type="process" path="git" arguments="config user.name &quot;unknown&quot;" /><!-- duration: 23ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.404" type="process" path="git" arguments="config user.email &quot;unknown&quot;" /><!-- duration: 23ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.430" type="process" path="git" arguments="config filter.lfs.clean &quot;git-lfs clean %f&quot;" /><!-- duration: 20ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.452" type="process" path="git" arguments="config filter.lfs.smudge &quot;git-lfs smudge %f&quot;" /><!-- duration: 19ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.473" type="process" path="git" arguments="config filter.lfs.required true" /><!-- duration: 19ms -->
      <step title="Configure git server" date="2025-07-23T11:08:17.494" >
        <step title="Executing external process" date="2025-07-23T11:08:17.496" type="process" path="git" arguments="config receive.denyCurrentBranch ignore" /><!-- duration: 20ms -->
      </step><!-- duration: 24ms -->
      <step title="Create deny users for .git folder" date="2025-07-23T11:08:17.520" /><!-- duration: 37ms -->
      <step title="Configure git-credential" date="2025-07-23T11:08:17.563" >
        <step title="Executing external process" date="2025-07-23T11:08:17.592" type="process" path="git" arguments="config credential.helper &quot;!&apos;/home/<USER>/repository/.git/hooks/git-credential-invalid.sh&apos;&quot;" /><!-- duration: 28ms -->
      </step><!-- duration: 59ms -->
      <step title="Setup post receive hook" date="2025-07-23T11:08:17.625" >
        <step title="Non-Windows enviroment, granting 755 permission to post-receive hook file" date="2025-07-23T11:08:17.645" /><!-- duration: 17ms -->
      </step><!-- duration: 40ms -->
    </step><!-- duration: 795ms -->
    <step title="Executing external process" date="2025-07-23T11:08:17.670" type="process" path="git" arguments="log -n 1 main --" >
      <step title="Process dump" date="2025-07-23T11:08:17.702" exitCode="128" type="processOutput" /><!-- duration: 2ms -->
    </step><!-- duration: 38ms -->
    <step title="Start deployment in the background" date="2025-07-23T11:08:17.711" >
      <step title="Executing external process" date="2025-07-23T11:08:17.724" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 14ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.741" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 12ms -->
      <step title="Executing external process" date="2025-07-23T11:08:17.756" type="process" path="git" arguments="log -n 1 main --" >
        <step title="Process dump" date="2025-07-23T11:08:17.780" exitCode="128" type="processOutput" /><!-- duration: 3ms -->
      </step><!-- duration: 30ms -->
      <step title="Acquiring Deployment Lock" date="2025-07-23T11:08:17.789" /><!-- duration: 2ms -->
      <step title="Acquired Deployment Lock" date="2025-07-23T11:08:17.835" /><!-- duration: 3ms -->
    </step><!-- duration: 218ms -->
  </step><!-- duration: 1090ms -->
  <step title="Outgoing response" date="2025-07-23T11:08:17.935" type="response" statusCode="202" statusText="Accepted" /><!-- duration: 2ms -->
</step><!-- duration: 1142ms -->
