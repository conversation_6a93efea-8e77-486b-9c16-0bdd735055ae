<step title="Incoming Request" date="2025-07-23T19:05:27.403" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,15" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="f2778614-67f7-11f0-80f1-848f4c0f5f6b" X-ARR-LOG-ID="f2778614-67f7-11f0-80f1-848f4c0f5f6b" CLIENT-IP="**************:10554" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="10554" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T19:05:27.425" /><!-- duration: 43ms -->
