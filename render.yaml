services:
  - type: web
    name: sentrycoin-v4-dual-strategy
    runtime: node
    plan: free
    buildCommand: "npm install"
    startCommand: "node src/index-v4.js"
    envVars:
      # Core System
      - key: SYMBOL
        value: SOLUSDT
      - key: EXCHANGE
        value: binance
      - key: NODE_ENV
        value: production
      - key: ALGORITHM_VERSION
        value: v4.0

      # Telegram Configuration
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_CHAT_ID
        sync: false
      - key: TELEGRAM_API_ID
        sync: false
      - key: TELEGRAM_API_HASH
        sync: false

      # Market Classifier Thresholds
      - key: PRESSURE_THRESHOLD
        value: "3.0"
      - key: LIQUIDITY_THRESHOLD
        value: "100000"
      - key: STRONG_MOMENTUM_THRESHOLD
        value: "-0.3"
      - key: WEAK_MOMENTUM_THRESHOLD
        value: "-0.1"
      - key: ORDER_BOOK_DEPTH
        value: "50"

      # Trading Configuration (PAPER TRADING ENABLED)
      - key: PAPER_TRADING
        value: "true"
      - key: TRIFECTA_TRADING_ENABLED
        value: "true"
      - key: SQUEEZE_TRADING_ENABLED
        value: "true"

      # Position Management
      - key: TRIFECTA_MAX_POSITION
        value: "1000"
      - key: TRIFECTA_STOP_LOSS
        value: "2.0"
      - key: TRIFECTA_TAKE_PROFIT
        value: "5.0"
      - key: SQUEEZE_MAX_POSITION
        value: "500"
      - key: SQUEEZE_STOP_LOSS
        value: "1.5"
      - key: SQUEEZE_TAKE_PROFIT
        value: "3.0"
      - key: SQUEEZE_TIME_EXIT
        value: "300"

      # System Configuration
      - key: COOLDOWN_MINUTES
        value: "5"
      - key: LOG_LEVEL
        value: info
      - key: STORAGE_TYPE
        value: memory
