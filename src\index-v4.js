#!/usr/bin/env node

/**
 * SentryCoin v4.0 Entry Point
 * 
 * The final evolution: From failed crash predictor to dual-strategy trading system
 * Based on comprehensive quantitative analysis of live-fire data
 */

import SentryCoinV4 from './sentrycoin-v4.js';
import express from 'express';
import dotenv from 'dotenv';

dotenv.config();

// Global system instance
let sentryCoinSystem = null;

/**
 * Main execution function
 */
async function main() {
  console.log('🛡️ SentryCoin v4.0 - Dual-Strategy Market Engine');
  console.log('📊 Market Microstructure Classification System');
  console.log('🎯 Trifecta Conviction + Absorption Squeeze Strategies\n');

  // Create Express app for monitoring and control
  const app = express();
  const port = process.env.PORT || 3000;
  
  app.use(express.json());

  // Health check endpoint
  app.get('/', (req, res) => {
    const status = sentryCoinSystem ? sentryCoinSystem.getSystemStatus() : { status: 'initializing' };
    res.json({
      service: 'SentryCoin v4.0 Dual-Strategy Engine',
      version: '4.0',
      status: sentryCoinSystem?.isRunning ? 'running' : 'stopped',
      uptime: status.uptime || 0,
      timestamp: new Date().toISOString(),
      system: status
    });
  });

  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      service: 'sentrycoin-v4',
      version: '4.0',
      timestamp: new Date().toISOString()
    });
  });

  // System status endpoint
  app.get('/status', (req, res) => {
    if (!sentryCoinSystem) {
      return res.status(503).json({ error: 'System not initialized' });
    }
    
    res.json(sentryCoinSystem.getSystemStatus());
  });

  // Trading performance endpoint
  app.get('/performance', (req, res) => {
    if (!sentryCoinSystem) {
      return res.status(503).json({ error: 'System not initialized' });
    }
    
    const status = sentryCoinSystem.getSystemStatus();
    res.json({
      trifectaTrading: status.trifectaTrader,
      squeezeTrading: status.squeezeTrader,
      classifier: status.classifier,
      timestamp: new Date().toISOString()
    });
  });

  // Classification statistics endpoint
  app.get('/classifications', (req, res) => {
    if (!sentryCoinSystem) {
      return res.status(503).json({ error: 'System not initialized' });
    }
    
    const status = sentryCoinSystem.getSystemStatus();
    res.json({
      classifier: status.classifier,
      totalSignals: (status.trifectaTrader?.signalsReceived || 0) + (status.squeezeTrader?.signalsReceived || 0),
      breakdown: {
        trifectaSignals: status.trifectaTrader?.signalsReceived || 0,
        squeezeSignals: status.squeezeTrader?.signalsReceived || 0
      },
      timestamp: new Date().toISOString()
    });
  });

  // System control endpoints
  app.post('/control/start', async (req, res) => {
    if (sentryCoinSystem?.isRunning) {
      return res.json({ message: 'System already running' });
    }
    
    try {
      if (!sentryCoinSystem) {
        sentryCoinSystem = new SentryCoinV4();
      }
      
      const started = await sentryCoinSystem.start();
      if (started) {
        res.json({ message: 'System started successfully' });
      } else {
        res.status(500).json({ error: 'Failed to start system' });
      }
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  app.post('/control/stop', async (req, res) => {
    if (!sentryCoinSystem?.isRunning) {
      return res.json({ message: 'System not running' });
    }
    
    try {
      await sentryCoinSystem.shutdown();
      res.json({ message: 'System stopped successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Configuration update endpoint
  app.post('/config/thresholds', (req, res) => {
    if (!sentryCoinSystem?.classifier) {
      return res.status(503).json({ error: 'Classifier not available' });
    }
    
    try {
      const { pressureThreshold, liquidityThreshold, strongMomentumThreshold, weakMomentumThreshold } = req.body;
      
      sentryCoinSystem.classifier.updateThresholds({
        pressureThreshold,
        liquidityThreshold,
        strongMomentumThreshold,
        weakMomentumThreshold
      });
      
      res.json({ message: 'Thresholds updated successfully' });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  });

  // Start Express server
  app.listen(port, () => {
    console.log(`🌐 SentryCoin v4.0 API server running on port ${port}`);
    console.log(`📡 Endpoints:`);
    console.log(`   Status: http://localhost:${port}/status`);
    console.log(`   Performance: http://localhost:${port}/performance`);
    console.log(`   Classifications: http://localhost:${port}/classifications`);
  });

  // Initialize and start the SentryCoin system
  try {
    sentryCoinSystem = new SentryCoinV4();
    const started = await sentryCoinSystem.start();
    
    if (started) {
      console.log('\n🎉 SentryCoin v4.0 is fully operational!');
      console.log('🧠 Market Classification: ACTIVE');
      console.log('🎯 Dual-Strategy Trading: MONITORING');
      console.log('📊 Real-time Analysis: RUNNING');
    } else {
      console.error('\n❌ Failed to start SentryCoin v4.0');
      console.log('🌐 API server will continue running for diagnostics');
    }
    
  } catch (error) {
    console.error('❌ Fatal error during startup:', error.message);
    console.log('🌐 API server will continue running for diagnostics');
  }

  // Handle graceful shutdown
  process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    if (sentryCoinSystem) {
      await sentryCoinSystem.shutdown();
    }
    process.exit(0);
  });
  
  process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    if (sentryCoinSystem) {
      await sentryCoinSystem.shutdown();
    }
    process.exit(0);
  });
  
  // Handle uncaught exceptions
  process.on('uncaughtException', async (error) => {
    console.error('❌ Uncaught Exception:', error);
    if (sentryCoinSystem) {
      await sentryCoinSystem.shutdown();
    }
    process.exit(1);
  });
  
  process.on('unhandledRejection', async (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    if (sentryCoinSystem) {
      await sentryCoinSystem.shutdown();
    }
    process.exit(1);
  });
}

// Start the application
main().catch(async (error) => {
  console.error('❌ Fatal startup error:', error);
  if (sentryCoinSystem) {
    await sentryCoinSystem.shutdown();
  }
  process.exit(1);
});

export default main;
