<step title="Incoming Request" date="2025-07-23T14:18:26.338" instance="fc17de" url="/deploy?scmType=ExternalGit&amp;isAsync=true" method="POST" type="request" pid="821,1,45" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Expect="100-continue" Content-Length="87" x-ms-request-id="f9169b22-f2c8-4d1b-a738-9d4fd24ad57a" X-ARR-LOG-ID="f9169b22-f2c8-4d1b-a738-9d4fd24ad57a" CLIENT-IP="*************:47360" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/deploy?scmType=ExternalGit&amp;isAsync=true" X-Client-IP="*************" X-Client-Port="47360" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:18:26.340" /><!-- duration: 5ms -->
  <step title="FetchHandler" date="2025-07-23T14:18:26.376" >
    <step title="Executing external process" date="2025-07-23T14:18:26.509" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 121ms -->
    <step title="Executing external process" date="2025-07-23T14:18:26.634" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 17ms -->
    <step title="Executing external process" date="2025-07-23T14:18:26.654" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 81ms -->
    <step title="Start deployment in the background" date="2025-07-23T14:18:26.745" >
      <step title="Executing external process" date="2025-07-23T14:18:26.773" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 15ms -->
      <step title="Executing external process" date="2025-07-23T14:18:26.792" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 13ms -->
      <step title="Executing external process" date="2025-07-23T14:18:26.809" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 54ms -->
      <step title="Acquiring Deployment Lock" date="2025-07-23T14:18:26.871" /><!-- duration: 3ms -->
      <step title="Acquired Deployment Lock" date="2025-07-23T14:18:26.933" /><!-- duration: 2ms -->
    </step><!-- duration: 269ms -->
  </step><!-- duration: 641ms -->
  <step title="Outgoing response" date="2025-07-23T14:18:27.019" type="response" statusCode="202" statusText="Accepted" /><!-- duration: 2ms -->
</step><!-- duration: 686ms -->
