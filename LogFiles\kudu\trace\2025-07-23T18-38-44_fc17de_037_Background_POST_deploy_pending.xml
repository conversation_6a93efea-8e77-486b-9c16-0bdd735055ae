<step title="BackgroundTrace" date="2025-07-23T18:38:44.034" instance="fc17de" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-23T18:38:44.145" /><!-- duration: 3ms -->
  <step title="Creating temporary deployment" date="2025-07-23T18:38:44.151" /><!-- duration: 51ms -->
  <step title="Performing fetch based deployment" date="2025-07-23T18:38:44.317" >
    <step title="Deployment timer started" date="2025-07-23T18:38:48.305" >
      <step title="DeploymentManager.Deploy(id:0cb039a6c15e6ecbfd29aa684d2f2a168eb06cde)" date="2025-07-23T18:38:48.313" >
        <step title="Collecting changeset information" date="2025-07-23T18:38:48.339" /><!-- duration: 104ms -->
        <step title="Updating submodules" date="2025-07-23T18:38:48.452" /><!-- duration: 1156ms -->
        <step title="Determining deployment builder" date="2025-07-23T18:38:49.776" >
          <step title="Builder is OryxBuilder" date="2025-07-23T18:38:49.787" /><!-- duration: 2ms -->
        </step><!-- duration: 15ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-23T18:38:49.828" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-23T18:38:49.916" >
            <step title="Building" date="2025-07-23T18:38:50.014" >
              <step title="Executing external process" date="2025-07-23T18:38:50.220" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddca182a86d65f -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 12889ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-23T18:39:03.428" /><!-- duration: 3ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-23T18:39:04.220" /><!-- duration: 13ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-23T18:39:04.254" /><!-- duration: 3ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-23T18:39:04.260" /><!-- duration: 5ms -->
            </step><!-- duration: 14896ms -->
          </step><!-- duration: 15000ms -->
          <step title="Cleaning up temp files" date="2025-07-23T18:39:04.918" /><!-- duration: 290ms -->
          <step title="Cleaning up temp files" date="2025-07-23T18:39:05.211" /><!-- duration: 506ms -->
          <step title="Reloading status file with latest updates" date="2025-07-23T18:39:05.738" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-23T18:39:05.741" /><!-- duration: 25ms -->
          </step><!-- duration: 31ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-23T18:39:05.779" /><!-- duration: 3ms -->
        </step><!-- duration: 16034ms -->
      </step><!-- duration: 17575ms -->
