<step title="BackgroundTrace" date="2025-07-23T11:08:17.714" instance="fc17de" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-23T11:08:17.841" /><!-- duration: 2ms -->
  <step title="Creating temporary deployment" date="2025-07-23T11:08:17.845" /><!-- duration: 79ms -->
  <step title="Performing fetch based deployment" date="2025-07-23T11:08:18.132" >
    <step title="Deployment timer started" date="2025-07-23T11:08:21.193" >
      <step title="DeploymentManager.Deploy(id:f7030834d0f5c8a3755996929a2347f0f5f39b3c)" date="2025-07-23T11:08:21.198" >
        <step title="Collecting changeset information" date="2025-07-23T11:08:21.241" /><!-- duration: 107ms -->
        <step title="Updating submodules" date="2025-07-23T11:08:21.353" /><!-- duration: 1118ms -->
        <step title="Determining deployment builder" date="2025-07-23T11:08:22.685" >
          <step title="Builder is OryxBuilder" date="2025-07-23T11:08:22.691" /><!-- duration: 2ms -->
        </step><!-- duration: 12ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-23T11:08:22.732" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-23T11:08:22.819" >
            <step title="Building" date="2025-07-23T11:08:22.978" >
              <step title="Executing external process" date="2025-07-23T11:08:23.204" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddc9d93d1f1cd4 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 26469ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-23T11:08:49.983" /><!-- duration: 5ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-23T11:08:50.882" /><!-- duration: 6ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-23T11:08:50.912" /><!-- duration: 3ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-23T11:08:50.927" /><!-- duration: 2ms -->
            </step><!-- duration: 28500ms -->
          </step><!-- duration: 28663ms -->
          <step title="Cleaning up temp files" date="2025-07-23T11:08:51.484" /><!-- duration: 332ms -->
          <step title="Cleaning up temp files" date="2025-07-23T11:08:51.820" /><!-- duration: 510ms -->
          <step title="Reloading status file with latest updates" date="2025-07-23T11:08:52.357" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-23T11:08:52.361" /><!-- duration: 67ms -->
          </step><!-- duration: 75ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-23T11:08:52.437" /><!-- duration: 2ms -->
        </step><!-- duration: 29786ms -->
      </step><!-- duration: 31345ms -->
