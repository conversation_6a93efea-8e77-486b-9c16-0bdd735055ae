<step title="Incoming Request" date="2025-07-23T14:29:46.928" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,31" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="760a0e22-67d1-11f0-bed0-848f4c0f5f6b" X-ARR-LOG-ID="760a0e22-67d1-11f0-bed0-848f4c0f5f6b" CLIENT-IP="**************:4965" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="4965" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:29:46.932" /><!-- duration: 13ms -->
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T14:29:46.949" /><!-- duration: 35ms -->
  <step title="Error occurred" date="2025-07-23T14:47:47.012" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 11ms -->
  <step title="Outgoing response" date="2025-07-23T14:47:47.034" type="response" statusCode="200" statusText="OK" /><!-- duration: 10ms -->
</step><!-- duration: 1080123ms -->
