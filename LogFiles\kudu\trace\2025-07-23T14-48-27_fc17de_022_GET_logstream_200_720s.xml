<step title="Incoming Request" date="2025-07-23T14:48:27.395" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,19" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="11d500a4-67d4-11f0-a340-848f4c0f5f6b" X-ARR-LOG-ID="11d500a4-67d4-11f0-a340-848f4c0f5f6b" CLIENT-IP="**************:29156" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="29156" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:48:27.402" /><!-- duration: 5ms -->
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T14:48:27.414" /><!-- duration: 31ms -->
  <step title="Error occurred" date="2025-07-23T15:00:27.451" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 6ms -->
  <step title="Outgoing response" date="2025-07-23T15:00:27.486" type="response" statusCode="200" statusText="OK" /><!-- duration: 4ms -->
</step><!-- duration: 720098ms -->
