<step title="Incoming Request" date="2025-07-24T01:15:24.449" instance="5d7cab" url="/logstream" method="GET" type="request" pid="821,1,5" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="8fd31033-682b-11f0-8b60-848f4c0f5f6b" X-ARR-LOG-ID="8fd31033-682b-11f0-8b60-848f4c0f5f6b" CLIENT-IP="**************:32972" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="32972" >
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-24T01:15:24.457" /><!-- duration: 73ms -->
