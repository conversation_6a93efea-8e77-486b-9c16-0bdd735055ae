<step title="Incoming Request" date="2025-07-23T14:47:36.628" instance="fc17de" url="/deploy?scmType=ExternalGit&amp;isAsync=true" method="POST" type="request" pid="821,1,7" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Expect="100-continue" Content-Length="87" x-ms-request-id="538b0a50-8b46-4b28-86f7-6712e5c41b3f" X-ARR-LOG-ID="538b0a50-8b46-4b28-86f7-6712e5c41b3f" CLIENT-IP="*************:47174" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/deploy?scmType=ExternalGit&amp;isAsync=true" X-Client-IP="*************" X-Client-Port="47174" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:47:36.631" /><!-- duration: 6ms -->
  <step title="FetchHandler" date="2025-07-23T14:47:36.676" >
    <step title="Executing external process" date="2025-07-23T14:47:36.775" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 72ms -->
    <step title="Executing external process" date="2025-07-23T14:47:36.852" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 15ms -->
    <step title="Executing external process" date="2025-07-23T14:47:36.869" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 69ms -->
    <step title="Start deployment in the background" date="2025-07-23T14:47:36.945" >
      <step title="Executing external process" date="2025-07-23T14:47:36.969" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 13ms -->
      <step title="Executing external process" date="2025-07-23T14:47:36.985" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 13ms -->
      <step title="Executing external process" date="2025-07-23T14:47:37.001" type="process" path="git" arguments="log -n 1 main --" /><!-- duration: 44ms -->
      <step title="Acquiring Deployment Lock" date="2025-07-23T14:47:37.047" /><!-- duration: 2ms -->
      <step title="Acquired Deployment Lock" date="2025-07-23T14:47:37.086" /><!-- duration: 2ms -->
    </step><!-- duration: 263ms -->
  </step><!-- duration: 534ms -->
  <step title="Outgoing response" date="2025-07-23T14:47:37.214" type="response" statusCode="202" statusText="Accepted" /><!-- duration: 3ms -->
</step><!-- duration: 593ms -->
