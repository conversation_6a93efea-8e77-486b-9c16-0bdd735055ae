<step title="Incoming Request" date="2025-07-23T14:20:12.089" instance="fc17de" url="/logstream" method="GET" type="request" pid="821,1,44" Host="sentrycoin-predictor-app.scm.azurewebsites.net" User-Agent="AZURECLI/2.75.0 (MSI)" Accept-Encoding="identity" Authorization="Bas..." x-ms-client-request-id="1f5d9d6d-67d0-11f0-92f7-848f4c0f5f6b" X-ARR-LOG-ID="1f5d9d6d-67d0-11f0-92f7-848f4c0f5f6b" CLIENT-IP="**************:24621" X-SITE-DEPLOYMENT-ID="sentrycoin-predictor-app" WAS-DEFAULT-HOSTNAME="sentrycoin-predictor-app.scm.azurewebsites.net" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/logstream" X-Client-IP="**************" X-Client-Port="24621" >
  <step title="Cleanup Xml Logs" date="2025-07-23T14:20:12.092" /><!-- duration: 5ms -->
  <step title="LogStreamHandlerMiddleware.Invoke" date="2025-07-23T14:20:12.103" /><!-- duration: 25ms -->
  <step title="Error occurred" date="2025-07-23T14:30:12.139" type="error" text="LogStreamManager: ProcessRequest end" /><!-- duration: 7ms -->
  <step title="Outgoing response" date="2025-07-23T14:30:12.152" type="response" statusCode="200" statusText="OK" /><!-- duration: 3ms -->
</step><!-- duration: 600068ms -->
